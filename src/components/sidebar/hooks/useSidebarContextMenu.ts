import { useState, useCallback, useRef, useEffect } from "react";
import { FileItem } from "../components/filelist";

// 右键菜单类型定义
export type ContextMenuType = "file" | "folder" | "sidebar-empty";

// 右键菜单操作接口
export interface ContextMenuAction {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  onClick: (data?: any) => void;
  textColor?: string;
  separator?: boolean;
  disabled?: boolean;
}

// 右键菜单数据接口
export interface ContextMenuData {
  type: ContextMenuType;
  position: { x: number; y: number };
  target?: FileItem | null;
  projectId?: string;
}

// Hook 返回值接口
export interface UseSidebarContextMenuReturn {
  // 菜单状态
  isOpen: boolean;
  menuData: ContextMenuData | null;
  contextMenuTarget: FileItem | null;

  // 菜单操作
  openMenu: (data: ContextMenuData) => void;
  closeMenu: () => void;

  // 事件处理器
  handleSidebarContextMenu: (e: React.MouseEvent) => void;
  handleFileContextMenu: (e: React.MouseEvent, file: FileItem) => void;

  // 菜单配置
  getMenuActions: (
    type: ContextMenuType,
    target?: FileItem | null
  ) => ContextMenuAction[];
}

/**
 * 统一的 Sidebar 右键菜单管理 Hook
 * 负责管理所有类型的右键菜单状态和操作
 */
export const useSidebarContextMenu = (
  projectId?: string
): UseSidebarContextMenuReturn => {
  const [isOpen, setIsOpen] = useState(false);
  const [menuData, setMenuData] = useState<ContextMenuData | null>(null);
  const [contextMenuTarget, setContextMenuTarget] = useState<FileItem | null>(
    null
  );
  const timeoutRef = useRef<NodeJS.Timeout>();

  // 打开菜单
  const openMenu = useCallback(
    (data: ContextMenuData) => {
      setMenuData({ ...data, projectId });
      setContextMenuTarget(data.target || null);
      setIsOpen(true);
    },
    [projectId]
  );

  // 关闭菜单
  const closeMenu = useCallback(() => {
    setIsOpen(false);
    setContextMenuTarget(null); // 立即清除目标项状态
    // 延迟清理数据，避免动画过程中数据丢失
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      setMenuData(null);
    }, 200);
  }, []);

  // 处理 Sidebar 空白处右键
  const handleSidebarContextMenu = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      openMenu({
        type: "sidebar-empty",
        position: { x: e.clientX, y: e.clientY },
        target: null,
      });
    },
    [openMenu]
  );

  // 处理文件/文件夹右键
  const handleFileContextMenu = useCallback(
    (e: React.MouseEvent, file: FileItem) => {
      e.preventDefault();
      e.stopPropagation();

      const menuType: ContextMenuType =
        file.type === "folder" ? "folder" : "file";

      openMenu({
        type: menuType,
        position: { x: e.clientX, y: e.clientY },
        target: file,
      });
    },
    [openMenu]
  );

  // 获取菜单操作配置
  const getMenuActions = useCallback(
    (type: ContextMenuType, target?: FileItem | null): ContextMenuAction[] => {
      // 这里先返回空数组，具体的菜单项配置将在后续实现
      // 实际使用时会根据不同的 type 返回对应的菜单项
      return [];
    },
    []
  );

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    isOpen,
    menuData,
    contextMenuTarget,
    openMenu,
    closeMenu,
    handleSidebarContextMenu,
    handleFileContextMenu,
    getMenuActions,
  };
};

export default useSidebarContextMenu;
