import React from "react";
import FileList from "./filelist";
import { Region } from "@/lib/api/types/common";
import { FileItem } from "./filelist";
import EmptyState from "@/components/ui/status/EmptyState";
import { getVirtualFolders } from "@/utils/virtualFolders";

interface TabContentProps {
  activeTabId: string;
  files: any[];
  onFileSelect: (file: any) => void;
  selectedFilePath?: string;
  onFileContextMenu?: (e: React.MouseEvent, file: FileItem) => void;
  projectId?: string;
  onSidebarContextMenu?: (e: React.MouseEvent) => void;
  contextMenuTarget?: FileItem | null;
}

const TabContent: React.FC<TabContentProps> = ({
  activeTabId,
  files,
  onFileSelect,
  selectedFilePath,
  onFileContextMenu,
  projectId,
  onSidebarContextMenu,
  contextMenuTarget,
}) => {
  // console.log("🏷️ TabContent received:", {
  //   projectId,
  //   activeTabId,
  //   filesCount: files.length,
  // });
  // console.log("📄 Files data sample:", files.slice(0, 2));

  // 检查特定 region 是否为空（没有文件也没有虚拟文件夹）
  const isRegionEmpty = (region: Region): boolean => {
    const regionFiles = files.filter((file) => file.region === region);
    const regionVirtualFolders = projectId
      ? getVirtualFolders(projectId).filter(
          (folder) => folder.region === region
        )
      : [];
    return regionFiles.length === 0 && regionVirtualFolders.length === 0;
  };

  // 根据不同的tab id显示不同的内容
  const getContentForTab = (id: string) => {
    switch (id) {
      case "personal":
        return isRegionEmpty(Region.PERSONAL) ? (
          <EmptyState
            title="No Files Yet"
            description="Drag to upload or right-click to create a new file."
            className="h-100 px-4 flex items-center justify-center"
            onContextMenu={onSidebarContextMenu}
          />
        ) : (
          <FileList
            files={files.filter((file) => file.region === Region.PERSONAL)}
            onFileSelect={onFileSelect}
            selectedFilePath={selectedFilePath}
            onFileContextMenu={onFileContextMenu}
            projectId={projectId}
            region={Region.PERSONAL}
            contextMenuTarget={contextMenuTarget}
          />
        );
      case "team":
        return isRegionEmpty(Region.SHARED) ? (
          <EmptyState
            title="No Shared Files Yet"
            description="Drag to upload or right-click to create a new file."
            className="h-100 px-4 flex items-center justify-center"
            onContextMenu={onSidebarContextMenu}
          />
        ) : (
          <FileList
            files={files.filter((file) => file.region === Region.SHARED)}
            onFileSelect={onFileSelect}
            selectedFilePath={selectedFilePath}
            onFileContextMenu={onFileContextMenu}
            projectId={projectId}
            region={Region.SHARED}
            contextMenuTarget={contextMenuTarget}
          />
        );
      case "laws":
        return isRegionEmpty(Region.REFERENCE) ? (
          <EmptyState
            title="No Reference Yet"
            description="Drag from Inbox, or upload a new reference file."
            className="h-100 px-4 flex items-center justify-center"
            onContextMenu={onSidebarContextMenu}
          />
        ) : (
          <FileList
            files={files.filter((file) => file.region === Region.REFERENCE)}
            onFileSelect={onFileSelect}
            selectedFilePath={selectedFilePath}
            onFileContextMenu={onFileContextMenu}
            projectId={projectId}
            region={Region.REFERENCE}
            contextMenuTarget={contextMenuTarget}
          />
        );
      case "inbox":
        return (
          <div className="p-4 text-center text-gray-500">
            Inbox content will be displayed here
          </div>
        );
      default:
        return (
          <div className="p-4 text-center text-gray-500">
            Please select a tab to view content
          </div>
        );
    }
  };

  return <div className="h-full w-full">{getContentForTab(activeTabId)}</div>;
};

export default TabContent;
