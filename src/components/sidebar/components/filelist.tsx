import React, { useState, useEffect, useRef } from "react";
import { ChevronRight } from "lucide-react";
// import { Icon } from "framesound-ui";
import { AnimatePresence, motion } from "framer-motion";
import FileIcon from "../../ui/file-icon";
import { getVirtualFolders } from "../../../utils/virtualFolders";

// 文件树节点结构，支持两种数据格式：
// 1. 树形结构数据（已处理的）
// 2. 后端返回的平铺路径数据（需要转换的）
export interface FileItem {
  id?: string;
  path?: string; // 树形结构中使用
  type?: "file" | "folder"; // 树形结构中使用
  children?: FileItem[];
  // 后端返回的原始数据字段
  created_at?: string;
  updated_at?: string;
  project_id?: string;
  region?: string;
  name?: string; // 后端返回的完整文件路径，如 "legal/test/civil_judgment_2024001.pdf"
  entity_type?: "file";
  // 虚拟文件夹标记
  isVirtual?: boolean;
}

interface FileListProps {
  files: any[]; // 支持 TreeItem[] 或 FileItem[]
  onFileSelect: (file: FileItem) => void;
  selectedFilePath?: string;
  onFileContextMenu?: (e: React.MouseEvent, file: FileItem) => void;
  projectId?: string; // 添加项目ID以支持虚拟文件夹
  region?: Region; // 添加区域参数以过滤虚拟文件夹
  contextMenuTarget?: FileItem | null; // 当前右键菜单的目标项
}

// 本地存储键名
const STORAGE_KEY = "framesound-folder-states";

// 从 path 中提取文件或文件夹名称
const getNameFromPath = (path: string): string => {
  // 移除尾部斜杠(如果有)
  const cleanPath = path.endsWith("/") ? path.slice(0, -1) : path;
  // 获取路径最后一部分
  return cleanPath.split("/").pop() || path;
};

import { Region } from "../../../lib/api/types/common";

// 将平铺的文件路径数据转换为树形结构
// 核心理念：只有文件是真实的，所有文件夹都是虚拟的
export const buildFileTree = (
  rawFiles: any[],
  projectId?: string,
  region?: Region
): FileItem[] => {
  const tree: FileItem[] = [];
  const folderMap = new Map<string, FileItem>();

  // 获取用户手动创建的虚拟文件夹
  const allVirtualFolders = projectId ? getVirtualFolders(projectId) : [];
  const userCreatedFolders = region
    ? allVirtualFolders.filter((folder) => folder.region === region)
    : allVirtualFolders;

  // 调试日志（已注释）
  // if (projectId) {
  //   console.log("🗂️ Virtual folders debug:", {
  //     region,
  //     allVirtualFolders: allVirtualFolders.length,
  //     filteredVirtualFolders: virtualFolders.length,
  //     virtualFolders,
  //   });
  // }

  // 为每个文件路径创建必要的文件夹结构
  rawFiles.forEach((rawFile, index) => {
    // 处理后端返回的数据格式（项目文件树端点返回的是 name 字段）
    const fileName = rawFile.name;
    // 根据文件名判断类型（后端数据中没有 entity_type）
    const fileType = "file"; // 后端返回的都是文档/文件，文件夹是虚拟的

    // console.log(`🔧 Processing file ${index + 1}/${rawFiles.length}:`, { fileName, fileId: rawFile.id, fileType });

    if (!fileName) return;

    // 对于简单的文件名（不包含路径分隔符），直接添加到根级别
    if (!fileName.includes("/")) {
      const fileItem: FileItem = {
        id: rawFile.id || `${projectId}-${index}`,
        path: `/${fileName}`,
        type: fileType,
        name: fileName,
        children: undefined, // 文件不需要 children
        // 保留原始数据
        ...rawFile,
      };
      tree.push(fileItem);
      return;
    }

    // 处理包含路径的文件名
    const pathParts = fileName.split("/").filter(Boolean);
    let currentPath = "";
    let currentLevel = tree;

    // 遍历路径的每一部分，创建文件夹结构
    pathParts.forEach((part: string, partIndex: number) => {
      const isLastPart = partIndex === pathParts.length - 1;
      currentPath = currentPath ? `${currentPath}/${part}` : `/${part}`;

      if (isLastPart) {
        // 这是文件
        const fileItem: FileItem = {
          id: rawFile.id || `${projectId}-${index}`,
          path: currentPath,
          type: fileType,
          name: part,
          children: undefined, // 文件不需要 children
          // 保留原始数据
          ...rawFile,
        };

        // console.log(`📄 Adding file to tree:`, { fileName: part, fullPath: currentPath, fileId: fileItem.id });
        currentLevel.push(fileItem);
        // console.log(`✅ File added. New currentLevel length:`, currentLevel.length);
      } else {
        // 这是文件夹
        let folderItem = folderMap.get(currentPath);
        if (!folderItem) {
          folderItem = {
            id: `folder-${currentPath}`,
            path: currentPath,
            type: "folder",
            name: part,
            children: [],
          };
          folderMap.set(currentPath, folderItem);
          currentLevel.push(folderItem);
        }
        currentLevel = folderItem.children!;
      }
    });
  });

  // 添加用户手动创建的虚拟文件夹到树结构中
  userCreatedFolders.forEach((virtualFolder) => {
    const pathParts = virtualFolder.path.split("/").filter(Boolean);
    let currentPath = "";
    let currentLevel = tree;

    pathParts.forEach((part: string, partIndex: number) => {
      const isLastPart = partIndex === pathParts.length - 1;
      currentPath = currentPath ? `${currentPath}/${part}` : `/${part}`;

      // 检查是否已存在（真实文件夹优先）
      let existingItem = currentLevel.find((item) => item.path === currentPath);

      if (!existingItem) {
        // 创建虚拟文件夹项
        const folderItem: FileItem = {
          id: isLastPart ? virtualFolder.id : `virtual-folder-${currentPath}`,
          path: currentPath,
          type: "folder",
          name: part,
          children: [],
          // 标记为虚拟文件夹
          isVirtual: true,
        };

        currentLevel.push(folderItem);
        existingItem = folderItem;
      } else {
        // 如果文件夹已存在，标记为虚拟（但保留现有的 children）
        if (!existingItem.isVirtual) {
          existingItem.isVirtual = true;
        }
        // 确保 children 数组存在
        if (!existingItem.children) {
          existingItem.children = [];
        }
      }

      if (existingItem.children) {
        currentLevel = existingItem.children;
      }
    });
  });

  // 对树进行排序：文件夹在前，文件在后，同类型按字母顺序排序
  const sortTree = (items: FileItem[]): FileItem[] => {
    return items
      .sort((a, b) => {
        // 文件夹优先
        if (a.type !== b.type) {
          return a.type === "folder" ? -1 : 1;
        }
        // 同类型按名称排序
        const aName = a.path ? getNameFromPath(a.path) : "";
        const bName = b.path ? getNameFromPath(b.path) : "";
        return aName.localeCompare(bName);
      })
      .map((item) => {
        if (item.children) {
          return { ...item, children: sortTree(item.children) };
        }
        return item;
      });
  };

  return sortTree(tree);
};

// 检查数据是否为后端返回的原始数据格式（需要转换为树形结构）
const isRawFileData = (files: any[]): boolean => {
  if (!files || files.length === 0) return false;
  // 检查是否为后端返回的数据格式（包含 id、region、name 等属性）
  const firstItem = files[0];
  return (
    firstItem.id !== undefined &&
    firstItem.region !== undefined &&
    firstItem.name !== undefined &&
    firstItem.created_at !== undefined
  );
};

// 从localStorage加载保存的状态
const loadExpandedStates = (): Map<string, boolean> => {
  try {
    // 检查是否在浏览器环境中
    if (typeof window !== "undefined") {
      const savedStates = localStorage.getItem(STORAGE_KEY);
      if (savedStates) {
        return new Map(JSON.parse(savedStates));
      }
    }
  } catch (error) {
    console.error("Error loading folder states from localStorage:", error);
  }
  return new Map<string, boolean>();
};

// 保存状态到localStorage
const saveExpandedStates = (states: Map<string, boolean>): void => {
  try {
    // 检查是否在浏览器环境中
    if (typeof window !== "undefined") {
      localStorage.setItem(
        STORAGE_KEY,
        JSON.stringify(Array.from(states.entries()))
      );
    }
  } catch (error) {
    console.error("Error saving folder states to localStorage:", error);
  }
};

// 使用全局Map来存储每个文件夹的展开状态
// 键是文件夹的路径，值是展开状态
const expandedStateMap = loadExpandedStates();

// 自定义CSS类名
const animationStyles = {
  container: "ml-2 my-0.5 overflow-hidden",
  expanded: "h-auto opacity-100",
  collapsed: "h-0 opacity-0",
  transition: "transition-all duration-300 ease-in-out",
};

const FileListItem: React.FC<{
  item: FileItem;
  level: number;
  onFileSelect: (file: FileItem) => void;
  selectedFilePath?: string;
  isLastItem?: boolean;
  onFileContextMenu?: (e: React.MouseEvent, file: FileItem) => void;
  contextMenuTarget?: FileItem | null;
}> = ({
  item,
  level,
  onFileSelect,
  selectedFilePath,
  isLastItem = false,
  onFileContextMenu,
  contextMenuTarget,
}) => {
  // 从全局Map获取状态，如果不存在则默认为展开状态(true)
  const [expanded, setExpanded] = useState(() => {
    if (item.path && expandedStateMap.has(item.path)) {
      return expandedStateMap.get(item.path) ?? true;
    }
    // 默认展开所有文件夹
    return true;
  });

  const itemRef = useRef<HTMLDivElement>(null);

  const isFolder = item.type === "folder";
  const isSelected = selectedFilePath === item.path;
  const isContextMenuTarget = contextMenuTarget?.path === item.path;
  const hasChildren = isFolder && item.children && item.children.length > 0;

  // 从路径中获取显示名称
  const displayName = item.path
    ? getNameFromPath(item.path)
    : item.name
    ? getNameFromPath(item.name)
    : "Unknown";

  // 确保必要的属性存在
  if (!item.path || !item.type) {
    console.warn(
      "FileListItem: Missing required properties (path or type)",
      item
    );
    return null;
  }

  // 当状态变化时，更新全局Map并保存到localStorage
  useEffect(() => {
    if (isFolder && item.path) {
      expandedStateMap.set(item.path, expanded);
      saveExpandedStates(expandedStateMap);
    }
  }, [expanded, item.path, isFolder]);

  const handleClick = () => {
    if (isFolder) {
      setExpanded(!expanded);
    } else {
      onFileSelect(item);
    }
  };

  // 右键点击事件处理
  const handleContextMenu = (e: React.MouseEvent) => {
    if (onFileContextMenu) {
      onFileContextMenu(e, item);
    }
  };

  return (
    <div className="file-item relative">
      {/* 垂直连接线 */}
      {level > 0 && (
        <div
          className="absolute border-l border-foreground/[0.07]"
          style={{
            left: `${(level - 1) * 8 + 2}px`,
            top: 0,
            bottom: 0,
            zIndex: 0,
          }}
        ></div>
      )}

      <div
        ref={itemRef}
        className={`flex items-center py-1 pl-2 my-0.5 rounded-md cursor-pointer ${
          isSelected
            ? "bg-foreground/5 hover:bg-foreground/10"
            : isContextMenuTarget
            ? "bg-foreground/5"
            : "hover:bg-foreground/5"
        } relative z-10`}
        style={{ marginLeft: `${level * 8 + 0}px` }}
        onClick={handleClick}
        onContextMenu={handleContextMenu}
      >
        {isFolder && hasChildren && (
          <motion.span
            animate={{ rotate: expanded ? 90 : 0 }}
            transition={{ type: "spring", bounce: 0, duration: 0.4 }}
            className="flex mr-1"
            style={{ width: "12px", height: "12px" }}
          >
            <ChevronRight className="w-3 h-3 text-gray-500" />
          </motion.span>
        )}
        <FileIcon
          fileName={displayName}
          filePath={item.path}
          fileType={item.type}
          className="w-4 h-4 mr-1.5 text-secondaryBtn"
          isFolderOpen={isFolder && expanded}
        />
        <span className="text-sm truncate">{displayName}</span>
      </div>

      {/* 子项目容器，使用 Framer Motion 实现流畅的动画效果 */}
      {hasChildren && (
        <AnimatePresence initial={false}>
          {expanded && (
            <motion.div
              initial={{ height: 0 }}
              animate={{ height: "auto" }}
              exit={{ height: 0 }}
              transition={{ type: "spring", bounce: 0, duration: 0.4 }}
              className="ml-2 overflow-hidden"
            >
              {item.children?.map((child, index) => (
                <FileListItem
                  key={child.id}
                  item={child}
                  level={level + 1}
                  onFileSelect={onFileSelect}
                  selectedFilePath={selectedFilePath}
                  isLastItem={index === item.children!.length - 1}
                  onFileContextMenu={onFileContextMenu}
                  contextMenuTarget={contextMenuTarget}
                />
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      )}
    </div>
  );
};

const FileList: React.FC<FileListProps> = ({
  files,
  onFileSelect,
  selectedFilePath,
  onFileContextMenu,
  projectId,
  region,
  contextMenuTarget,
}) => {
  // 处理数据：如果是平铺路径格式则转换为树形结构
  const processedFiles = React.useMemo(() => {
    // console.log("📋 Processing files:", {
    //   files,
    //   projectId,
    //   isRawFileData: isRawFileData(files),
    // });

    // 如果是原始文件数据或者是空数组（需要添加虚拟文件夹），都调用 buildFileTree
    if (isRawFileData(files) || (Array.isArray(files) && projectId)) {
      const result = buildFileTree(files, projectId, region);
      // console.log("🌳 Built file tree:", result);
      return result;
    }
    // console.log("⚠️ Returning files as-is:", files);
    return files;
  }, [files, projectId, region]);

  return (
    <div className="file-list overflow-y-auto">
      {processedFiles.map((file, index) => (
        <FileListItem
          key={file.id}
          item={file}
          level={0}
          onFileSelect={onFileSelect}
          selectedFilePath={selectedFilePath}
          isLastItem={index === processedFiles.length - 1}
          onFileContextMenu={onFileContextMenu}
          contextMenuTarget={contextMenuTarget}
        />
      ))}
    </div>
  );
};

export default FileList;
