# API Client Request Queue Management

## 概述

为了解决项目中可能出现的并发请求问题，我们在 `ApiClient` 中实现了请求队列管理机制。该机制确保所有通过 `apiClient` 发送的请求按顺序执行，避免并发冲突。

**⚠️ 重要说明**：此机制仅管理通过我们自己的 `apiClient` 发送的请求。外部 SDK（如 `@quote/auth-client/react` 的 `verify_status` 请求）不在此管理范围内。

## 架构设计

### 🏗️ 核心组件

- **RequestQueue 类**：队列管理器，负责请求的排队、去重和执行
- **ApiClient 类**：API 客户端，集成队列管理，保持向后兼容
- **QueuedRequest 接口**：队列项数据结构，包含请求信息和回调

### 🚀 核心机制

- **串行执行**：所有请求按顺序执行，避免并发冲突
- **智能去重**：基于请求特征自动合并相同请求
- **优先级调度**：支持高优先级请求优先处理
- **超时管理**：自动处理请求超时和取消
- **错误隔离**：单个请求失败不影响队列中其他请求

### ⚙️ 配置选项

```typescript
interface QueueConfig {
  enabled?: boolean; // 是否启用队列，默认 true
  maxSize?: number; // 队列最大长度，默认 100
  timeout?: number; // 请求超时时间，默认 30000ms
  enableDeduplication?: boolean; // 是否启用请求去重，默认 true
}
```

## 技术实现

### 🔄 请求流程

1. **请求入队**：调用 `apiClient.request()` 时，请求被添加到队列
2. **去重检查**：检查是否存在相同的待处理请求
3. **优先级排序**：按优先级和时间戳对队列进行排序
4. **串行执行**：队列处理器按顺序执行请求
5. **结果分发**：将响应结果分发给所有相关的 Promise

### 🔍 去重算法

```typescript
// 请求唯一标识生成规则
const generateRequestId = (endpoint: string, config: RequestConfig): string => {
  const method = config.method || "GET";
  const bodyStr = config.body ? JSON.stringify(config.body) : "";
  const headersStr = JSON.stringify(config.headers || {});

  return method === "GET"
    ? `${method}:${endpoint}:${headersStr}`
    : `${method}:${endpoint}:${headersStr}:${bodyStr}`;
};
```

## 开发指南

### 📊 队列状态监控

```typescript
// 获取队列状态
const status = apiClient.getQueueStatus();
console.log("Queue status:", {
  queueLength: status.queueLength,
  isProcessing: status.isProcessing,
  pendingRequestsCount: status.pendingRequestsCount,
});

// 清空队列（紧急情况下使用）
apiClient.clearQueue();
```

### 🛠️ 自定义配置

```typescript
import { ApiClient } from "@/lib/api/base/apiClient";

// 创建自定义配置的客户端
const customClient = new ApiClient("https://api.example.com", {
  enabled: true,
  maxSize: 50,
  timeout: 15000,
  enableDeduplication: true,
});

// 创建不使用队列的客户端（特殊场景）
const directClient = new ApiClient("https://api.example.com", {
  enabled: false,
});
```

### 🔄 实际使用示例

```typescript
// 项目中的实际使用（自动启用队列）
import { api } from "@/lib/api";

// 这些请求都会通过队列管理
await api.project.getProjects(); // 通过队列
await api.user.getProfile(); // 通过队列
await api.system.healthCheck(); // 通过队列

// 如果同时发送多个相同请求，会自动去重
const [result1, result2] = await Promise.all([
  api.system.healthCheck(),
  api.system.healthCheck(), // 这个会被去重，不会发送重复请求
]);
```

## 队列管理范围

### ✅ 管理范围内

- 所有通过 `apiClient` 发送的请求
- 项目服务：`api.project.*`
- 用户服务：`api.user.*`
- 系统服务：`api.system.*`
- 文档服务：`api.doc.*`
- 文件服务：`api.file.*`

### ❌ 管理范围外

- 外部 SDK 请求：`@quote/auth-client/react` 的 `verify_status`
- 直接使用 `fetch()` 的请求
- 第三方库的内部请求

## 优先级系统

### 优先级规则

- 数字越小优先级越高（1 > 2 > 3 > ...）
- 默认优先级为 10
- 相同优先级按时间顺序执行

### 🎯 优先级使用建议

```typescript
// 高优先级：关键业务请求
await apiClient.priorityRequest("/api/critical-data", { priority: 1 });

// 普通优先级：常规业务请求（默认）
await apiClient.get("/api/projects"); // priority: 10

// 低优先级：日志、统计等非关键请求
await apiClient.request("/api/analytics", { priority: 20 });
```

## 故障排查

### ⚠️ 常见问题

#### 1. 请求超时

```typescript
// 问题：请求超时
// 原因：网络慢或服务器响应慢
// 解决：检查网络连接，或增加超时时间

const customClient = new ApiClient(undefined, { timeout: 60000 }); // 60秒
```

#### 2. 队列积压

```typescript
// 问题：队列长度持续增长
// 原因：请求处理速度 < 请求产生速度
// 解决：检查服务器性能，或减少并发请求

const status = apiClient.getQueueStatus();
if (status.queueLength > 20) {
  console.warn("Queue backlog detected:", status);
}
```

#### 3. 去重失效

```typescript
// 问题：相同请求没有被去重
// 原因：请求参数微小差异（如时间戳）
// 解决：检查请求参数，确保真正相同的请求参数一致
```

## 团队开发指南

### 🎯 最佳实践

1. **合理使用优先级**：关键业务请求使用高优先级（1-5），常规请求使用默认优先级（10），非关键请求使用低优先级（15-20）
2. **监控队列健康**：在开发过程中定期检查 `getQueueStatus()`，确保队列长度正常
3. **避免绕过队列**：不要直接使用 `fetch()` 或其他 HTTP 客户端，统一使用 `apiClient`
4. **测试去重效果**：在开发时验证相同请求是否被正确去重

### 📊 性能基准

- **正常队列长度**：< 10 个请求
- **处理间隔**：10ms（防止服务器过载）
- **去重效果**：可减少 30-50% 重复请求
- **超时时间**：默认 30 秒，可根据接口特性调整

### 🔧 开发工具

```typescript
// 开发环境下的调试代码
if (process.env.NODE_ENV === "development") {
  // 监控队列状态
  setInterval(() => {
    const status = apiClient.getQueueStatus();
    if (status.queueLength > 10) {
      console.warn("Queue backlog:", status);
    }
  }, 5000);
}
```

### 📝 日志说明

队列管理器的日志格式：

- `[API-Queue] 📥 Request enqueued` - 请求入队
- `[API-Queue] 🚀 Starting queue processing` - 开始处理
- `[API-Queue] ⚡ Executing request` - 执行请求
- `[API-Queue] ✅ Request completed` - 请求完成
- `[API-Queue] 🔄 Request deduplicated` - 请求去重

### 🚨 注意事项

1. **外部 SDK 不受管理**：`@quote/auth-client/react` 等外部 SDK 的请求不在队列管理范围内
2. **向后兼容**：现有代码无需修改，队列功能自动启用
3. **紧急情况**：可使用 `apiClient.clearQueue()` 清空队列，但会导致待处理请求失败
4. **配置修改**：修改队列配置需要重新创建 `ApiClient` 实例
