/**
 * API 使用示例
 * 展示如何使用新的 API 架构
 */

import { api, ApiError, ApiErrorType } from "../index";
import { ProjectStatus, Region } from "../types/common";

// 示例1: 直接使用服务
export async function exampleDirectService() {
  try {
    // 获取项目列表
    const projects = await api.project.getProjects();
    console.log("项目列表:", projects);

    // 健康检查
    const health = await api.system.healthCheck();
    console.log("系统状态:", health);
  } catch (error) {
    if (error instanceof ApiError) {
      console.error("API错误:", error.message, error.type);

      // 根据错误类型处理
      switch (error.type) {
        case ApiErrorType.VALIDATION_ERROR:
          console.error("验证错误详情:", error.details);
          break;
        case ApiErrorType.UNAUTHORIZED:
          console.error("需要重新登录");
          break;
        case ApiErrorType.NETWORK_ERROR:
          console.error("网络连接问题");
          break;
        default:
          console.error("未知错误");
      }
    }
  }
}

// 示例3: 项目操作流程
export async function exampleProjectWorkflow() {
  try {
    // 1. 创建项目
    const newProject = await api.project.createProject({
      name: "新项目",
      description: "项目描述",
      status: ProjectStatus.ARCHIVED, // 基于API文档的枚举值
    });
    console.log("创建的项目:", newProject);

    // 2. 获取项目文件树 - 暂时注释掉，等待API支持
    // const tree = await api.project.getProjectTree(newProject.id);
    // console.log("项目文件树:", tree);

    // 3. 更新项目收藏状态 - 暂时注释掉，等待API支持
    // await api.project.updatePersonalization(newProject.id, {
    //   is_favorite: true,
    // });
    // console.log("项目已设为收藏");

    // 4. 更新项目信息
    const updatedProject = await api.project.updateProject(newProject.id, {
      name: "更新后的项目名称",
    });
    console.log("更新后的项目:", updatedProject);
  } catch (error) {
    console.error("项目操作失败:", error);
  }
}

// 示例4: 文档操作流程
export async function exampleDocWorkflow() {
  try {
    // 1. 创建文档
    const newDoc = await api.doc.createDoc({
      project_id: "project-id",
      region: Region.PERSONAL,
      doc_name: "新文档",
      content: "文档内容",
    });
    console.log("创建的文档:", newDoc);

    // 2. 获取文档详情
    const doc = await api.doc.getDoc(newDoc.id);
    console.log("文档详情:", doc);

    // 3. 更新文档
    const updatedDoc = await api.doc.updateDoc(newDoc.id, {
      doc_name: "更新后的文档名称",
      content: "更新后的内容",
    });
    console.log("更新后的文档:", updatedDoc);

    // 4. 删除文档
    const deleteResult = await api.doc.deleteDoc(newDoc.id);
    console.log("文档删除结果:", deleteResult);
  } catch (error) {
    console.error("文档操作失败:", error);
  }
}

// 示例5: 文件操作流程
export async function exampleFileWorkflow() {
  try {
    // 1. 创建文件
    const newFile = await api.file.createFile({
      project_id: "project-id",
      region: Region.PERSONAL,
      file_name: "新文件.txt",
      content: "文件内容",
      metadata: { type: "text" },
    });
    console.log("创建的文件:", newFile);

    // 2. 获取文件详情
    const file = await api.file.getFile(newFile.id);
    console.log("文件详情:", file);

    // 3. 更新文件
    const updatedFile = await api.file.updateFile(newFile.id, {
      id: newFile.id,
      file_name: "更新后的文件名.txt",
      content: "更新后的内容",
    });
    console.log("更新后的文件:", updatedFile);

    // 4. 移动文件到其他项目
    const movedFile = await api.file.moveFile(newFile.id, {
      id: "user-id",
      project_id: "target-project-id",
    });
    console.log("移动后的文件:", movedFile);
  } catch (error) {
    console.error("文件操作失败:", error);
  }
}
