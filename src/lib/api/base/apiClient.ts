/**
 * API 客户端基础配置
 * 提供统一的 HTTP 请求配置和拦截器
 * 支持请求队列管理，确保请求按顺序执行
 */

import logger from "@/utils/logger";

// 创建 API 客户端专用的 logger
const apiLogger = logger.createPrefixed("API");
const queueLogger = logger.createPrefixed("API-Queue");

// 获取 API 基础 URL - 使用内部 API 路由代理
const getApiBaseUrl = (): string => {
  // 使用 Next.js API 路由作为代理，避免暴露真实后端地址
  if (typeof window !== "undefined") {
    // 客户端：使用相对路径访问 API 路由
    return "/api/proxy";
  } else {
    // 服务端：从私有环境变量获取真实 API 地址
    const baseUrl = process.env.API_BASE_URL;
    if (!baseUrl) {
      throw new Error("环境变量 API_BASE_URL 未设置");
    }
    return baseUrl;
  }
};

// API 基础配置
export const API_CONFIG = {
  BASE_URL: getApiBaseUrl(),
  TIMEOUT: 10000,
  HEADERS: {
    "Content-Type": "application/json",
  },
} as const;

// 请求配置接口
export interface RequestConfig {
  method?: "GET" | "POST" | "PUT" | "PATCH" | "DELETE";
  headers?: Record<string, string>;
  body?: any;
  signal?: AbortSignal;
  priority?: number; // 请求优先级，数字越小优先级越高
}

// 响应接口
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
}

// 队列配置接口
export interface QueueConfig {
  enabled?: boolean; // 是否启用队列，默认 true
  maxSize?: number; // 队列最大长度，默认 100
  timeout?: number; // 请求超时时间，默认 30000ms
  enableDeduplication?: boolean; // 是否启用请求去重，默认 true
}

// 队列中的请求项
interface QueuedRequest<T = any> {
  id: string;
  endpoint: string;
  config: RequestConfig;
  resolve: (value: ApiResponse<T>) => void;
  reject: (error: any) => void;
  priority: number;
  timestamp: number;
  abortController?: AbortController;
}

/**
 * 请求队列管理器
 * 确保所有请求按顺序执行，避免并发问题
 */
class RequestQueue {
  private queue: QueuedRequest[] = [];
  private isProcessing = false;
  private pendingRequests = new Map<string, QueuedRequest[]>();
  private config: Required<QueueConfig>;

  constructor(config: QueueConfig = {}) {
    this.config = {
      enabled: config.enabled ?? true,
      maxSize: config.maxSize ?? 100,
      timeout: config.timeout ?? 30000,
      enableDeduplication: config.enableDeduplication ?? true,
    };
  }

  /**
   * 添加请求到队列
   */
  enqueue<T = any>(
    endpoint: string,
    config: RequestConfig,
    executor: (
      endpoint: string,
      config: RequestConfig
    ) => Promise<ApiResponse<T>>
  ): Promise<ApiResponse<T>> {
    return new Promise<ApiResponse<T>>((resolve, reject) => {
      // 如果队列被禁用，直接执行请求
      if (!this.config.enabled) {
        executor(endpoint, config).then(resolve).catch(reject);
        return;
      }

      // 检查队列大小限制
      if (this.queue.length >= this.config.maxSize) {
        reject(
          new Error(`Request queue is full (max: ${this.config.maxSize})`)
        );
        return;
      }

      const requestId = this.generateRequestId(endpoint, config);
      const priority = config.priority ?? 10; // 默认优先级为 10
      const timestamp = Date.now();

      // 创建 AbortController 用于超时控制
      const abortController = new AbortController();
      const timeoutId = setTimeout(() => {
        abortController.abort();
        reject(new Error(`Request timeout after ${this.config.timeout}ms`));
      }, this.config.timeout);

      const queuedRequest: QueuedRequest<T> = {
        id: requestId,
        endpoint,
        config: {
          ...config,
          signal: config.signal || abortController.signal,
        },
        resolve: (value) => {
          clearTimeout(timeoutId);
          resolve(value);
        },
        reject: (error) => {
          clearTimeout(timeoutId);
          reject(error);
        },
        priority,
        timestamp,
        abortController,
      };

      // 请求去重处理
      if (this.config.enableDeduplication) {
        if (this.pendingRequests.has(requestId)) {
          // 相同请求已存在，合并回调
          this.pendingRequests.get(requestId)!.push(queuedRequest);
          queueLogger.log("🔄 Request deduplicated:", { requestId, endpoint });
          return;
        } else {
          this.pendingRequests.set(requestId, [queuedRequest]);
        }
      }

      // 添加到队列并按优先级排序
      this.queue.push(queuedRequest);
      this.queue.sort((a, b) => {
        if (a.priority !== b.priority) {
          return a.priority - b.priority; // 数字越小优先级越高
        }
        return a.timestamp - b.timestamp; // 相同优先级按时间排序
      });

      queueLogger.log("📥 Request enqueued:", {
        requestId,
        endpoint,
        priority,
        queueLength: this.queue.length,
      });

      // 开始处理队列
      this.processQueue(executor);
    });
  }

  /**
   * 处理队列中的请求
   */
  private async processQueue<T = any>(
    executor: (
      endpoint: string,
      config: RequestConfig
    ) => Promise<ApiResponse<T>>
  ): Promise<void> {
    // 如果正在处理或队列为空，直接返回
    if (this.isProcessing || this.queue.length === 0) {
      return;
    }

    this.isProcessing = true;
    queueLogger.log("🚀 Starting queue processing:", {
      queueLength: this.queue.length,
    });

    while (this.queue.length > 0) {
      const request = this.queue.shift()!;
      const requestId = request.id;

      try {
        queueLogger.log("⚡ Executing request:", {
          requestId,
          endpoint: request.endpoint,
          method: request.config.method || "GET",
        });

        // 检查请求是否已被取消
        if (request.config.signal?.aborted) {
          throw new Error("Request was aborted");
        }

        // 执行请求
        const result = await executor(request.endpoint, request.config);

        // 处理所有相同的请求（去重场景）
        const duplicatedRequests = this.pendingRequests.get(requestId) || [
          request,
        ];
        duplicatedRequests.forEach((req) => {
          req.resolve(result);
        });

        queueLogger.log("✅ Request completed:", {
          requestId,
          endpoint: request.endpoint,
          duplicatedCount: duplicatedRequests.length,
        });

        // 清理去重记录
        this.pendingRequests.delete(requestId);
      } catch (error) {
        queueLogger.error("❌ Request failed:", {
          requestId,
          endpoint: request.endpoint,
          error,
        });

        // 处理所有相同的请求（去重场景）
        const duplicatedRequests = this.pendingRequests.get(requestId) || [
          request,
        ];
        duplicatedRequests.forEach((req) => {
          req.reject(error);
        });

        // 清理去重记录
        this.pendingRequests.delete(requestId);
      }

      // 短暂延迟，避免过于频繁的请求
      await new Promise((resolve) => setTimeout(resolve, 10));
    }

    this.isProcessing = false;
    queueLogger.log("🏁 Queue processing completed");
  }

  /**
   * 生成请求唯一标识
   */
  private generateRequestId(endpoint: string, config: RequestConfig): string {
    const method = config.method || "GET";
    const bodyStr = config.body ? JSON.stringify(config.body) : "";
    const headersStr = JSON.stringify(config.headers || {});

    // 对于 GET 请求，只考虑 endpoint 和 headers
    // 对于其他请求，还要考虑 body
    const identifier =
      method === "GET"
        ? `${method}:${endpoint}:${headersStr}`
        : `${method}:${endpoint}:${headersStr}:${bodyStr}`;

    return identifier;
  }

  /**
   * 获取队列状态信息
   */
  getQueueStatus() {
    return {
      queueLength: this.queue.length,
      isProcessing: this.isProcessing,
      pendingRequestsCount: this.pendingRequests.size,
      config: this.config,
    };
  }

  /**
   * 清空队列
   */
  clearQueue() {
    // 取消所有待处理的请求
    this.queue.forEach((request) => {
      request.abortController?.abort();
      request.reject(new Error("Queue cleared"));
    });

    // 清空队列和去重记录
    this.queue = [];
    this.pendingRequests.clear();
    this.isProcessing = false;

    queueLogger.log("🧹 Queue cleared");
  }
}

/**
 * 基础 API 客户端类
 */
export class ApiClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;
  private requestQueue: RequestQueue;

  constructor(baseURL?: string, queueConfig?: QueueConfig) {
    this.baseURL = baseURL || API_CONFIG.BASE_URL;
    this.defaultHeaders = { ...API_CONFIG.HEADERS };
    this.requestQueue = new RequestQueue(queueConfig);
  }

  /**
   * 发送 HTTP 请求（通过队列管理）
   */
  async request<T = any>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    // 使用队列管理请求
    return this.requestQueue.enqueue(endpoint, config, (endpoint, config) =>
      this.executeRequest<T>(endpoint, config)
    );
  }

  /**
   * 实际执行 HTTP 请求的方法
   */
  private async executeRequest<T = any>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    const { method = "GET", headers = {}, body, signal } = config;

    const requestHeaders = {
      ...this.defaultHeaders,
      ...headers,
      // 明确指定接受的编码格式，避免压缩问题
      "Accept-Encoding": "gzip, deflate, br, identity",
      Accept: "application/json, text/plain, */*",
    };

    const requestInit: RequestInit = {
      method,
      headers: requestHeaders,
      signal,
    };

    if (body && method !== "GET") {
      requestInit.body = JSON.stringify(body);
    }

    try {
      apiLogger.log("🌐 发送请求:", { url, method, headers: requestHeaders });
      const response = await fetch(url, requestInit);
      apiLogger.log("📡 收到响应:", {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
      });

      let data: T;
      const contentType = response.headers.get("content-type");
      const contentEncoding = response.headers.get("content-encoding");

      apiLogger.log("📋 响应头信息:", { contentType, contentEncoding });

      // 特殊处理204 No Content响应
      if (response.status === 204) {
        apiLogger.log("✅ 204 No Content 响应，无需解析响应体");
        data = undefined as unknown as T;
      } else if (contentType && contentType.includes("application/json")) {
        try {
          // 对于 gzip 压缩的响应，先检查是否可以正常读取
          if (contentEncoding === "gzip") {
            apiLogger.log("🗜️ 检测到 gzip 压缩响应，尝试解析...");
          }

          data = await response.json();
          apiLogger.log("✅ JSON解析成功:", data);
        } catch (jsonError) {
          apiLogger.error("❌ JSON解析失败:", jsonError);
          apiLogger.error("响应详情:", {
            status: response.status,
            statusText: response.statusText,
            contentType,
            contentEncoding,
            url: response.url,
          });

          // 尝试作为文本读取以获取更多信息
          try {
            const responseClone = response.clone();
            const textContent = await responseClone.text();
            apiLogger.error(
              "原始响应内容:",
              textContent.substring(0, 200) + "..."
            );
          } catch (textError) {
            apiLogger.error("无法读取响应文本:", textError);
          }

          throw new Error(`Failed to parse JSON response: ${jsonError}`);
        }
      } else {
        try {
          data = (await response.text()) as unknown as T;
          apiLogger.log("✅ 文本解析成功:", data);
        } catch (textError) {
          apiLogger.error("❌ 文本解析失败:", textError);
          throw new Error(`Failed to parse text response: ${textError}`);
        }
      }

      if (!response.ok) {
        // 详细记录错误信息，特别是422验证错误
        if (response.status === 422) {
          apiLogger.error("🚨 422 验证错误详情:", {
            状态码: response.status,
            状态文本: response.statusText,
            请求URL: url,
            请求方法: method,
            请求体: body,
            响应数据: data,
          });
        }

        const error = new Error(
          `HTTP ${response.status}: ${response.statusText}`
        );
        // 将响应数据附加到错误对象上，供错误处理器使用
        (error as any).responseData = data;
        (error as any).status = response.status;
        throw error;
      }

      return {
        data,
        status: response.status,
        statusText: response.statusText,
      };
    } catch (error) {
      apiLogger.error("🚨 请求失败:", error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("Unknown error occurred");
    }
  }

  /**
   * GET 请求
   */
  get<T = any>(
    endpoint: string,
    config?: Omit<RequestConfig, "method" | "body">
  ) {
    return this.request<T>(endpoint, { ...config, method: "GET" });
  }

  /**
   * POST 请求
   */
  post<T = any>(
    endpoint: string,
    body?: any,
    config?: Omit<RequestConfig, "method">
  ) {
    return this.request<T>(endpoint, { ...config, method: "POST", body });
  }

  /**
   * PUT 请求
   */
  put<T = any>(
    endpoint: string,
    body?: any,
    config?: Omit<RequestConfig, "method">
  ) {
    return this.request<T>(endpoint, { ...config, method: "PUT", body });
  }

  /**
   * PATCH 请求
   */
  patch<T = any>(
    endpoint: string,
    body?: any,
    config?: Omit<RequestConfig, "method">
  ) {
    return this.request<T>(endpoint, { ...config, method: "PATCH", body });
  }

  /**
   * DELETE 请求
   */
  delete<T = any>(
    endpoint: string,
    config?: Omit<RequestConfig, "method" | "body">
  ) {
    return this.request<T>(endpoint, { ...config, method: "DELETE" });
  }

  /**
   * 获取请求队列状态
   */
  getQueueStatus() {
    return this.requestQueue.getQueueStatus();
  }

  /**
   * 清空请求队列
   */
  clearQueue() {
    this.requestQueue.clearQueue();
  }

  /**
   * 发送高优先级请求
   */
  async priorityRequest<T = any>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, priority: 1 });
  }
}

// 默认 API 客户端实例（启用队列管理）
export const apiClient = new ApiClient(undefined, {
  enabled: true,
  maxSize: 100,
  timeout: 15000,
  enableDeduplication: true,
});

// 创建不使用队列的 API 客户端实例（用于特殊场景）
export const directApiClient = new ApiClient(undefined, {
  enabled: false,
});
